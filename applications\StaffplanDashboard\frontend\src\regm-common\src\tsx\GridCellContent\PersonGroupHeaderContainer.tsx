// @ts-strict-null-checks: true
import * as React from 'react';
import { useState } from 'react';
import { ICellRenderData, IRowData } from '../../../../_common/application/tsx/GridBase';
import { IAQLParameters, IDataParameters } from '../../../../_common/platform/ts/AqlUtility';
import { nullHandlingBackend } from '../../../../_common/platform/ts/nullHandling';
import { IAQLFilter, IPeriodFilter } from '../../../../_common/platform/tsx/filtering/Interfaces';
import { StaffplanCellProps } from '../staffPlanCellContent';
import { ADD_MOCK_DATA, getPgMessages } from './mockData';
import { EmptyHeader, PersonGroupHeader } from './PersonGroupHeader';
import { apiInterval2UiInterval, toYYYYMMDD } from './PersonGroupHeaderUtils';
import { Error } from './styles';
import { errorBase, ErrorBase, isError, MessageType, PersonGroupData, PersonGroupMessage } from './types';
import { stringify } from './utils';

/** Specify value type. */
type PgCellProps = ICellRenderData<IRowData, string, string[]>;

/** Type guard for the PgCellProps type. */
const isPgCellProps = (props: StaffplanCellProps): props is PgCellProps => {
    const partials = [];
    partials.push(typeof props.value === 'string');
    partials.push(typeof props.values[0] === 'string');
    partials.push(typeof props.values[1] === 'string');
    return partials.every(Boolean);
};

interface PGHContainerProps {
    cellProps: StaffplanCellProps;
    cellType: 'day' | 'week';
}

/**
 * Container component for the person group header.
 * Fetches the person group data and renders the PersonGroupHeader component.
 */
export const PersonGroupHeaderContainer: React.FC<PGHContainerProps> = (props) => {
    const { cellProps, cellType } = props;
    const personGroupName = getPersonGroupName(cellProps);
    const { data, isLoading } = useGetPersonGroupData(cellProps);

    // If null or loading:
    if (!data || isLoading) return <EmptyHeader personGroupName={personGroupName} isLoading={isLoading} />;

    // In case of error:
    if (isError(data)) {
        return <Error error={data} />;
    }
    if (!data.messages) data.messages = [];

    // Add mock data if needed:
    if (ADD_MOCK_DATA) {
        const mockMessages = getPgMessages(15);
        data.messages = data.messages.concat(mockMessages);
    }

    return <PersonGroupHeader pgData={data} cellType={cellType} />;
};

interface PersonGroupDataResult {
    data: PersonGroupData | ErrorBase | null;
    isLoading: boolean;
}

/**
 * Custom hook to fetch person group data only once.
 */
const useGetPersonGroupData = (cellProps: StaffplanCellProps): PersonGroupDataResult => {
    const { glob } = cellProps;
    const [data, setData] = useState<PersonGroupData | ErrorBase | null>(null);
    const [isLoading, setIsLoading] = useState(false);

    if (!isPgCellProps(cellProps)) {
        const msg = 'Invalid types in "cellProps" in person group header';
        console.warn(msg);
        setData(errorBase(msg));
        return { data, isLoading };
    }

    if (!data && !isLoading) {
        setIsLoading(true);
        // Check if glob and required properties exist before proceeding
        if (!glob?.GlobalStates?.[0]?.getPeriodFilters?.()?.[0]) {
            console.warn('Global object not fully initialized yet, will retry on next render');
            setIsLoading(false);
            setData(null);
            return { data, isLoading };
        }

        getPersonGroupData(cellProps)
            .then((result) => {
                setData(result);
                setIsLoading(false);
            })
            .catch((error: Error) => {
                const msg = `Error fetching person group data:`;
                console.error(msg, error);
                setData(errorBase(`${msg} ${error.message || stringify(error)}`));
                setIsLoading(false);
            });
    }
    return { data, isLoading };
};

const getPersonGroupName = (cellProps: PgCellProps): string => {
    // This value may be wrong, but it is what we have for now.
    // More work may be required on this task to get the correct value:
    // https://support.pdc.com/portal/#issue/92330
    return cellProps.value;
};

/**
 * Fetches person group data from the backend.
 */
const getPersonGroupData = async (cellProps: PgCellProps): Promise<PersonGroupData | ErrorBase> | null => {
    const { glob, theme, data } = cellProps;
    const pgId: number = data?.items?.[0][4];
    const personGroupName = getPersonGroupName(cellProps);

    if (!glob) {
        console.error('No global object found');
        return errorBase('No global object found');
    }
    // For collapsed groups, there is no pgId, ie it is not an error:
    if (!pgId) return null;

    const dataSourceID = glob?.EntityModelsStore?.getDataSourceIDByName?.('PDCPLAN_DW');
    if (!dataSourceID) {
        console.error('No data source found');
        return errorBase('No data source found');
    }

    // Get the period filter and handle error if not found:
    const globalPeriodFilter = glob?.GlobalStates?.[0]?.getPeriodFilters?.()?.[0];
    if (!globalPeriodFilter) {
        console.error('No period filter found');
        return errorBase('No period filter found');
    }

    const filter = createFilters({ globalPeriodFilter, pgId, dataSourceID });
    

    // Create data parameters:
    const aqlParams: IAQLParameters = glob.AQLQueries.getAQLParametersFromTheme(theme, null, false);
    const dataParams: IDataParameters = {
        dataSourceID,
        attrib_refs: [
            { attrib: ['persongroup'] },
            { attrib: ['persongroup', '^persongroup_message', 'posted'] },
            { attrib: ['persongroup', '^persongroup_message', 'expires'] },
            { attrib: ['persongroup', '^persongroup_message', 'subject'] },
            { attrib: ['persongroup', '^persongroup_message', 'pdctext'] }
        ],
        group_attribs: [],
        filter,
        nullHandling: nullHandlingBackend.RETURN_NULLS
    };

    const params: IDataParameters = { ...aqlParams, ...dataParams };

    // Load data from api:
    const msgdata = await glob.AQLQueries?.loadData(params, {});
    if (!msgdata) {
        console.error('No response from AQL query');
        return errorBase('No response from AQL query');
    }

    const messages = mapToPersonGroupMessages(msgdata?.result || [], personGroupName);

    // Map the data to person group messages:

    // The global period filter appears to be in local time zone already:
    const fromTo = { fromDate: '', toDate: '' };
    if (globalPeriodFilter.from && globalPeriodFilter.to) {
        // We know from and to are not nil because we just checked
        const { from, to } = apiInterval2UiInterval(globalPeriodFilter as { from: string; to: string });
        fromTo.fromDate = from ? toYYYYMMDD(from) : '';
        fromTo.toDate = to ? toYYYYMMDD(to) : '';
    }

    // Create the person group data and return it:
    const result: PersonGroupData = {
        personGroupName,
        messages: messages || [],
        period: fromTo
    };
    console.log('PersonGroupHeaderContainer: getPersonGroupData: result', result);
    return result;
};

/** Map the AQL result to person group messages */
const mapToPersonGroupMessages = (apiResult: string[][], personGroupName: string): PersonGroupMessage[] => {
    // const messages: PersonGroupMessage[] = msgdata?.result
    const messages: PersonGroupMessage[] = apiResult
        ?.map((res) => {
            // Convert all dates below to local time zone,
            // and take to/from dates into account:
            const { from: postedDateTime, to: expiresDateTime } = apiInterval2UiInterval({ from: res[1], to: res[2] });
            // If no expires date, use posted date to show it only on the day it was posted:
            const expiresDateString = expiresDateTime ? toYYYYMMDD(expiresDateTime) : toYYYYMMDD(postedDateTime);

            const pgMsg: PersonGroupMessage = {
                personGroupName,
                messageText: res[4],
                subjectText: res[3],
                postedDateString: postedDateTime ? toYYYYMMDD(postedDateTime) : '',
                postedDateTime,
                expiresDateString,
                expiresDateTime,
                messageType: MessageType.PersonaleGruppe
            };
            return pgMsg;
        })
        // Remove empty messages:
        .filter((mes) => !!mes.messageText);
    return messages;
};

interface CreateFilters {
    globalPeriodFilter: IPeriodFilter;
    pgId: number;
    dataSourceID: number;
}

/**
 * Create filters for the person group header data query.
 * Using only the period filter, will include too many messages,
 * i.e. messages posted before the period with expires=null.
 * This makes sure to exclude those. Messages that have
 * expires=null are intented to expire on the day they are posted.
 */
const createFilters = ({ globalPeriodFilter, pgId, dataSourceID }: CreateFilters): IAQLFilter[] => {
    const aqlGroupIdFilter: IAQLFilter = {
        type: 'relation',
        attrib: ['persongroup', 'dwid'],
        relation: '=',
        val: pgId,
        dataSourceID
    };

    // Get messages posted before the end of the period:
    const aqlPostedBeforeEndFilter: IAQLFilter = {
        type: 'relation',
        attrib: ['persongroup', '^persongroup_message', 'posted'],
        relation: '<=',
        val: globalPeriodFilter.to,
        dataSourceID
    };

    // Get messages posted after the start of the period:
    const aqlPostedAfterStartFilter: IAQLFilter = {
        type: 'relation',
        attrib: ['persongroup', '^persongroup_message', 'posted'],
        relation: '>=',
        val: globalPeriodFilter.from,
        dataSourceID
    };

    // Exclude messages posted before the period that have expires=null:
    const aqlComplexFilter = {
        syntaxTree: {
            func: {
                name: 'or',
                params: [
                    // Either: Message was posted within the period (regardless of expiration)
                    {
                        func: {
                            name: '>=',
                            params: [
                                { attr: ['persongroup', '^persongroup_message', 'posted'] },
                                { var: [globalPeriodFilter.from] }
                            ]
                        }
                    },
                    // OR: For messages posted before the period, expires must not be null
                    {
                        func: {
                            name: 'and',
                            params: [
                                {
                                    func: {
                                        name: '<',
                                        params: [
                                            { attr: ['persongroup', '^persongroup_message', 'posted'] },
                                            { var: [globalPeriodFilter.from] }
                                        ]
                                    }
                                },
                                {
                                    func: {
                                        name: 'not',
                                        params: [
                                            {
                                                func: {
                                                    name: 'isnull',
                                                    params: [
                                                        { attr: ['persongroup', '^persongroup_message', 'expires'] }
                                                    ]
                                                }
                                            }
                                        ]
                                    }
                                }
                            ]
                        }
                    }
                ]
            }
        },
        rettype: 'logical',
        type: 'logical'
    };

    const filters: IAQLFilter[] = [
        aqlGroupIdFilter,
        aqlPostedBeforeEndFilter,
        aqlPostedAfterStartFilter,
        aqlComplexFilter
    ];
    return filters;
};
